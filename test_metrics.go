package main

import (
	"fmt"
	"net/http"
	"time"

	mt "github.com/HydraXTrader/exchange-generic-aeron-sdk-go/metrics"
)

func main() {
	// 启动HTTP服务器
	mux := http.NewServeMux()
	
	// 注册metrics端点
	mux.Handle("/metrics", mt.GetMetricsHandler())
	
	// 添加一个简单的健康检查端点
	mux.HandleFunc("/health", func(w http.ResponseWriter, r *http.Request) {
		w.WriteHeader(http.StatusOK)
		w.Write([]byte("OK"))
	})
	
	// 添加一个根路径处理器
	mux.HandleFunc("/", func(w http.ResponseWriter, r *http.Request) {
		w.WriteHeader(http.StatusOK)
		w.Write([]byte("Metrics server is running. Visit /metrics for Prometheus metrics."))
	})

	// 启动服务器
	server := &http.Server{
		Addr:    ":8080",
		Handler: mux,
	}

	fmt.Println("Starting metrics server on :8080")
	fmt.Println("Metrics endpoint: http://localhost:8080/metrics")
	fmt.Println("Health endpoint: http://localhost:8080/health")
	
	// 在后台生成一些示例metrics
	go func() {
		for {
			// 记录一些示例metrics
			mt.RecordSendSuccess("test-client", "cluster")
			mt.RecordConnectionSuccess("test-client", "cluster")
			mt.RecordSubscriberFragmentsPolled("test-subscriber", 10)
			
			time.Sleep(5 * time.Second)
		}
	}()

	if err := server.ListenAndServe(); err != nil {
		fmt.Printf("Server failed to start: %v\n", err)
	}
}
